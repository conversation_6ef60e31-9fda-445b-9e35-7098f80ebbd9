<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schedule - Community Valorant Tournaments</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/schedule.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>VALORANT<span class="accent">COMMUNITY</span></h1>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">HOME</a>
                </li>
                <li class="nav-item">
                    <a href="teams.html" class="nav-link">TEAMS</a>
                </li>
                <li class="nav-item">
                    <a href="schedule.html" class="nav-link active">SCHEDULE</a>
                </li>
                <li class="nav-item">
                    <a href="bracket.html" class="nav-link">BRACKET</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="schedule-page-header">
        <div class="container">
            <div class="header-content">
                <h1 class="page-title">TOURNAMENT SCHEDULE</h1>
                <p class="page-description">Complete match schedule for Neon Mania 2025</p>

                <!-- Tournament Progress -->
                <div class="tournament-progress">
                    <div class="progress-info">
                        <span class="progress-label">Tournament Progress</span>
                        <span class="progress-percentage" id="tournament-progress">75%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 75%"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Schedule Navigation -->
    <section class="schedule-nav">
        <div class="container">
            <div class="nav-tabs">
                <button class="nav-tab active" data-tab="overview">OVERVIEW</button>
                <button class="nav-tab" data-tab="detailed">DETAILED RESULTS</button>
                <button class="nav-tab" data-tab="timeline">TIMELINE</button>
            </div>
        </div>
    </section>

    <!-- Schedule Filters -->
    <section class="schedule-filters">
        <div class="container">
            <div class="filters-wrapper">
                <div class="filter-group">
                    <label for="round-filter">Round</label>
                    <select id="round-filter" class="filter-select">
                        <option value="all">All Rounds</option>
                        <option value="Quarterfinals">Quarterfinals</option>
                        <option value="Semifinals">Semifinals</option>
                        <option value="Grand Final">Grand Final</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="status-filter">Status</label>
                    <select id="status-filter" class="filter-select">
                        <option value="all">All Matches</option>
                        <option value="upcoming">Upcoming</option>
                        <option value="live">Live</option>
                        <option value="completed">Completed</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="date-filter">Date</label>
                    <select id="date-filter" class="filter-select">
                        <option value="all">All Dates</option>
                        <option value="2024-03-15">March 15, 2024</option>
                        <option value="2024-03-16">March 16, 2024</option>
                        <option value="2024-03-17">March 17, 2024</option>
                    </select>
                </div>

                <div class="filter-actions">
                    <button class="btn-secondary" onclick="clearFilters()">Clear Filters</button>
                    <button class="btn-primary" onclick="exportSchedule()">Export Schedule</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Tab Content Container -->
    <div class="tab-content-container">
        <!-- Overview Tab -->
        <section class="tab-content active" id="overview-tab">
            <div class="container">
                <div class="schedule-overview">
                    <div class="schedule-table-wrapper">
                        <table class="schedule-table">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Round</th>
                                    <th>Match</th>
                                    <th>Score</th>
                                    <th>Status</th>
                                    <th>Duration</th>
                                    <th>Winner</th>
                                </tr>
                            </thead>
                            <tbody id="schedule-tbody">
                                <!-- Schedule rows will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <!-- Detailed Results Tab -->
        <section class="tab-content" id="detailed-tab">
            <div class="container">
                <div class="detailed-results">
                    <h2 class="section-title">DETAILED MATCH RESULTS</h2>
                    <div class="matches-detailed" id="detailed-matches">
                        <!-- Detailed match cards will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Timeline Tab -->
        <section class="tab-content" id="timeline-tab">
            <div class="container">
                <div class="tournament-timeline">
                    <h2 class="section-title">TOURNAMENT TIMELINE</h2>
                    <div class="timeline-wrapper">
                        <div class="timeline" id="tournament-timeline">
                            <!-- Timeline will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Community Valorant Tournaments. Not affiliated with Riot Games.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data/teams.js"></script>
    <script src="js/data/tournaments.js"></script>
    <script src="js/components/navigation.js"></script>
    <script src="js/components/match-card.js"></script>
    <script src="js/pages/schedule.js"></script>
</body>
</html>
