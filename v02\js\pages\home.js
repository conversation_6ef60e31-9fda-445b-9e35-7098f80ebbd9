/**
 * HOME PAGE FUNCTIONALITY
 *
 * Handles the home page display including upcoming matches,
 * recent results, and tournament information.
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeHomePage();
});

function initializeHomePage() {
    loadTournamentInfo();
    loadUpcomingMatches();
    loadRecentResults();
    updateTournamentStats();
    loadTournamentInfoSection();

    // Set up auto-refresh for live matches (every 30 seconds)
    setInterval(refreshLiveContent, 30000);
}

/**
 * Load and display upcoming matches
 */
function loadUpcomingMatches() {
    const container = document.getElementById('upcoming-matches');
    if (!container) {
        console.error('Upcoming matches container not found');
        return;
    }

    // Get next 3 upcoming matches
    const upcomingMatches = getUpcomingMatchesLimit(3);

    if (upcomingMatches.length === 0) {
        container.innerHTML = `
            <div class="no-matches">
                <h3>No Upcoming Matches</h3>
                <p>All matches have been completed or the tournament has ended.</p>
            </div>
        `;
        return;
    }

    // Render match cards
    renderMatchCards(upcomingMatches, container, false);

    // Add click interactions
    addMatchCardInteractions(container);
}

/**
 * Load and display recent match results
 */
function loadRecentResults() {
    const container = document.getElementById('recent-results');
    if (!container) {
        console.error('Recent results container not found');
        return;
    }

    // Get last 3 completed matches
    const recentMatches = getRecentMatches(3);

    if (recentMatches.length === 0) {
        container.innerHTML = `
            <div class="no-matches">
                <h3>No Recent Results</h3>
                <p>No matches have been completed yet.</p>
            </div>
        `;
        return;
    }

    // Render match cards with details
    renderMatchCards(recentMatches, container, true);

    // Add click interactions
    addMatchCardInteractions(container);
}

/**
 * Load tournament information into hero section
 */
function loadTournamentInfo() {
    const tournament = getCurrentTournament();
    if (!tournament) return;

    // Update page title
    const pageTitle = document.getElementById('page-title');
    if (pageTitle) pageTitle.textContent = `${tournament.name} - ${tournament.subtitle}`;

    // Update hero title and subtitle
    const titleElement = document.getElementById('tournament-name');
    const subtitleElement = document.getElementById('tournament-subtitle');
    const descriptionElement = document.getElementById('tournament-description');

    if (titleElement) titleElement.textContent = tournament.name.toUpperCase();
    if (subtitleElement) subtitleElement.textContent = tournament.subtitle.toUpperCase();
    if (descriptionElement) descriptionElement.textContent = tournament.description;
}

/**
 * Update tournament statistics in the hero section
 */
function updateTournamentStats() {
    const tournament = getCurrentTournament();
    if (!tournament) return;

    // Update tournament progress
    const progress = getTournamentProgress();

    // Update hero stats with specific IDs
    const teamsCountElement = document.getElementById('teams-count');
    const prizePoolElement = document.getElementById('prize-pool');
    const tournamentDaysElement = document.getElementById('tournament-days');
    const tournamentDaysLabelElement = document.getElementById('tournament-days-label');

    if (teamsCountElement) teamsCountElement.textContent = teams.length.toString();
    if (prizePoolElement) prizePoolElement.textContent = tournament.prizePool;

    // Calculate days remaining or elapsed
    const startDate = new Date(tournament.startDate);
    const endDate = new Date(tournament.endDate);
    const today = new Date();

    let daysText;
    if (today < startDate) {
        const daysUntilStart = Math.ceil((startDate - today) / (1000 * 60 * 60 * 24));
        daysText = `${daysUntilStart} days until start`;
    } else if (today <= endDate) {
        const daysRemaining = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
        daysText = daysRemaining > 0 ? `${daysRemaining} days left` : 'Final day!';
    } else {
        daysText = 'Tournament ended';
    }

    if (tournamentDaysElement) tournamentDaysElement.textContent = daysText.split(' ')[0];
    if (tournamentDaysLabelElement) tournamentDaysLabelElement.textContent = daysText.split(' ').slice(1).join(' ').toUpperCase();
}

/**
 * Update a stat element by index
 * @param {string} selector - CSS selector for stat elements
 * @param {number} index - Index of the element to update
 * @param {string} value - New value to set
 */
function updateStatElement(selector, index, value) {
    const elements = document.querySelectorAll(selector);
    if (elements[index]) {
        elements[index].textContent = value;
    }
}

/**
 * Refresh live content (matches, scores, etc.)
 */
function refreshLiveContent() {
    const liveMatches = getLiveMatches();

    if (liveMatches.length > 0) {
        console.log('Refreshing live matches:', liveMatches.length);

        // Update upcoming matches section
        const upcomingContainer = document.getElementById('upcoming-matches');
        if (upcomingContainer) {
            updateLiveMatches(upcomingContainer);
        }

        // Update recent results section
        const resultsContainer = document.getElementById('recent-results');
        if (resultsContainer) {
            updateLiveMatches(resultsContainer);
        }
    }
}

/**
 * Handle tournament status updates
 */
function handleTournamentStatusUpdate() {
    const tournament = getCurrentTournament();

    switch (tournament.status) {
        case 'upcoming':
            showTournamentCountdown();
            break;
        case 'active':
            showActiveTournamentInfo();
            break;
        case 'completed':
            showTournamentResults();
            break;
    }
}

/**
 * Show countdown to tournament start
 */
function showTournamentCountdown() {
    const tournament = getCurrentTournament();
    const startDate = new Date(tournament.startDate);
    const now = new Date();

    if (startDate > now) {
        const timeUntilStart = startDate - now;
        const days = Math.floor(timeUntilStart / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeUntilStart % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

        // Update hero section with countdown
        const heroDescription = document.querySelector('.hero-description');
        if (heroDescription) {
            heroDescription.textContent = `Tournament starts in ${days} days and ${hours} hours (IST)`;
        }
    }
}

/**
 * Show active tournament information
 */
function showActiveTournamentInfo() {
    const nextMatch = getNextMatch();

    if (nextMatch) {
        const heroDescription = document.querySelector('.hero-description');
        if (heroDescription) {
            const team1 = getTeamById(nextMatch.team1);
            const team2 = getTeamById(nextMatch.team2);

            if (team1 && team2) {
                heroDescription.textContent = `Next up: ${team1.name} vs ${team2.name}`;
            }
        }
    }
}

/**
 * Show tournament completion results
 */
function showTournamentResults() {
    const tournament = getCurrentTournament();
    const finalMatch = tournament.bracket.final;

    if (finalMatch.winner) {
        const winner = getTeamById(finalMatch.winner);
        const heroDescription = document.querySelector('.hero-description');

        if (heroDescription && winner) {
            heroDescription.textContent = `Tournament Champion: ${winner.name}!`;
        }
    }
}

/**
 * Load tournament information section
 */
function loadTournamentInfoSection() {
    const tournament = getCurrentTournament();
    if (!tournament) return;

    const container = document.getElementById('tournament-info-grid');
    if (!container) return;

    // Format dates for display
    const startDate = new Date(tournament.startDate);
    const endDate = new Date(tournament.endDate);
    const formatDate = (date) => date.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
    });

    // Create tournament info cards
    container.innerHTML = `
        <div class="info-card">
            <h3>TOURNAMENT FORMAT</h3>
            <p>${tournament.format}</p>
            <p>Best of 3 matches</p>
            <p>Best of 5 Grand Final</p>
        </div>
        <div class="info-card">
            <h3>SCHEDULE</h3>
            <p>${formatDate(startDate)} - ${formatDate(endDate)}</p>
            ${Object.entries(tournament.schedule).map(([round, date]) =>
                `<p>${round}: ${new Date(date).toLocaleDateString('en-US', { month: 'long', day: 'numeric' })}</p>`
            ).join('')}
        </div>
        <div class="info-card">
            <h3>RULES</h3>
            ${tournament.rules.slice(0, 3).map(rule => `<p>${rule}</p>`).join('')}
        </div>
    `;
}

/**
 * Add scroll animations for better UX
 */
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe sections for animation
    const sections = document.querySelectorAll('.upcoming-matches, .recent-results, .tournament-info');
    sections.forEach(section => {
        observer.observe(section);
    });
}

// Initialize scroll animations when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    addScrollAnimations();
});

/**
 * Render match cards
 * @param {Array} matches - Array of matches to display
 * @param {HTMLElement} container - Container element
 * @param {boolean} showDetails - Whether to show match details
 */
function renderMatchCards(matches, container, showDetails) {
    container.innerHTML = '';

    matches.forEach(match => {
        const team1 = getTeamById(match.team1);
        const team2 = getTeamById(match.team2);

        if (!team1 || !team2) return;

        const card = document.createElement('div');
        card.className = `match-card ${match.status}`;
        card.setAttribute('data-match-id', match.id);

        const matchTime = TournamentTime.formatISTTime(match.matchDetails.scheduledTime);
        const score = getMatchScoreString(match);

        card.innerHTML = `
            <div class="match-header">
                <span class="match-time">${matchTime}</span>
                <span class="match-status ${match.status}">${match.status.toUpperCase()}</span>
            </div>
            <div class="match-teams">
                <div class="team ${match.winner === match.team1 ? 'winner' : ''}">
                    <div class="team-logo">${team1.logo}</div>
                    <span class="team-name">${team1.name}</span>
                </div>
                <div class="match-score">
                    <span class="score">${score}</span>
                </div>
                <div class="team ${match.winner === match.team2 ? 'winner' : ''}">
                    <div class="team-logo">${team2.logo}</div>
                    <span class="team-name">${team2.name}</span>
                </div>
            </div>
            ${showDetails && match.status === 'completed' ? `
                <div class="match-details">
                    <div class="match-info">
                        <span class="round">${match.matchDetails.round}</span>
                        ${TournamentTime.getMatchDuration(match) ? 
                            `<span class="duration">${TournamentTime.getMatchDuration(match)} min</span>` : ''}
                    </div>
                    ${match.matchDetails.maps ? `
                        <div class="maps-played">
                            ${match.matchDetails.maps.map(map => `
                                <span class="map">${map.name}</span>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            ` : ''}
        `;

        // Add click handler for match details
        card.addEventListener('click', () => showMatchDetails(match.id));

        container.appendChild(card);
    });
}

// Export functions for use in other scripts
window.HomePage = {
    initializeHomePage,
    loadTournamentInfo,
    loadUpcomingMatches,
    loadRecentResults,
    updateTournamentStats,
    loadTournamentInfoSection,
    refreshLiveContent
};
