/**
 * MATCH CARD COMPONENT
 *
 * Creates and manages match card displays for upcoming matches and results.
 * Used across multiple pages (home, schedule).
 */

/**
 * Create a match card element
 * @param {Object} match - Match data object (unified structure)
 * @param {boolean} showDetails - Whether to show detailed information
 * @returns {HTMLElement} - Match card element
 */
function createMatchCard(match, showDetails = false) {
    const team1 = getTeamById(match.team1);
    const team2 = getTeamById(match.team2);

    if (!team1 || !team2) {
        console.warn('Team not found for match:', match);
        return createPlaceholderCard();
    }

    const card = document.createElement('div');
    card.className = 'match-card';
    card.setAttribute('data-match-id', match.id);

    // Determine if team1 or team2 won (for styling)
    const team1Won = match.winner === match.team1;
    const team2Won = match.winner === match.team2;

    // Get match scores
    const score1 = match.matchDetails.score ? match.matchDetails.score.team1 : 0;
    const score2 = match.matchDetails.score ? match.matchDetails.score.team2 : 0;

    card.innerHTML = `
        <div class="match-header">
            <span class="match-time">${formatMatchTime(match.matchDetails.scheduledTime)}</span>
            <span class="match-status ${match.status}">${match.status.toUpperCase()}</span>
        </div>

        <div class="match-teams">
            <div class="team ${team1Won ? 'winner' : ''}">
                <div class="team-logo">${team1.logo}</div>
                <span class="team-name">${team1.name}</span>
            </div>

            <div class="match-score">
                ${match.status === 'completed' ? 
                    `<span class="score ${match.winner ? 'winner' : ''}">${score1} - ${score2}</span>` :
                    '<span class="score">VS</span>'}
            </div>

            <div class="team ${team2Won ? 'winner' : ''}">
                <div class="team-logo">${team2.logo}</div>
                <span class="team-name">${team2.name}</span>
            </div>
        </div>

        ${match.status === 'completed' && showDetails ? createMatchDetailsHTML(match) : ''}

        <div class="match-details">
            <span class="match-map">${getMatchMapInfo(match)}</span>
            <span class="match-round">${match.matchDetails.round}</span>
        </div>
    `;

    return card;
}

/**
 * Create detailed match information HTML
 * @param {Object} match - Match data object (unified structure)
 * @returns {string} - HTML string for match details
 */
function createMatchDetailsHTML(match) {
    if (!match.matchDetails.maps || match.matchDetails.maps.length === 0) {
        return '';
    }

    const mapsHTML = match.matchDetails.maps.map(map => {
        const team1 = getTeamById(match.team1);
        const team2 = getTeamById(match.team2);
        const mapWinner = getTeamById(map.winner);

        return `
            <div class="map-result">
                <span class="map-name">${map.name}</span>
                <span class="map-score">${map.score.team1}-${map.score.team2}</span>
                <span class="map-winner">${mapWinner ? mapWinner.name : 'TBD'}</span>
            </div>
        `;
    }).join('');

    return `
        <div class="match-maps">
            <h4>Map Results:</h4>
            ${mapsHTML}
        </div>
    `;
}

/**
 * Get map information for display
 * @param {Object} match - Match data object (unified structure)
 * @returns {string} - Map information string
 */
function getMatchMapInfo(match) {
    if (match.status === 'upcoming') {
        return 'Maps TBD';
    }

    if (match.matchDetails.maps && match.matchDetails.maps.length > 0) {
        return `${match.matchDetails.maps.length} map${match.matchDetails.maps.length > 1 ? 's' : ''} played`;
    }

    return 'Map info unavailable';
}

/**
 * Create a placeholder card for missing data
 * @returns {HTMLElement} - Placeholder card element
 */
function createPlaceholderCard() {
    const card = document.createElement('div');
    card.className = 'match-card placeholder';
    card.innerHTML = `
        <div class="match-header">
            <span class="match-time">TBD</span>
            <span class="match-status upcoming">TBD</span>
        </div>

        <div class="match-teams">
            <div class="team">
                <div class="team-logo">?</div>
                <span class="team-name">TBD</span>
            </div>

            <div class="vs-divider">VS</div>

            <div class="team">
                <div class="team-logo">?</div>
                <span class="team-name">TBD</span>
            </div>
        </div>

        <div class="match-details">
            <span class="match-map">Maps TBD</span>
            <span class="match-round">TBD</span>
        </div>
    `;

    return card;
}

/**
 * Render multiple match cards to a container
 * @param {Array} matches - Array of match objects
 * @param {HTMLElement} container - Container element to render cards into
 * @param {boolean} showDetails - Whether to show detailed information
 */
function renderMatchCards(matches, container, showDetails = false) {
    if (!container) {
        console.error('Container element not found');
        return;
    }

    // Clear existing content
    container.innerHTML = '';

    if (!matches || matches.length === 0) {
        container.innerHTML = `
            <div class="no-matches">
                <p>No matches to display</p>
            </div>
        `;
        return;
    }

    // Create and append match cards
    matches.forEach(match => {
        const card = createMatchCard(match, showDetails);
        container.appendChild(card);
    });
}

/**
 * Add click handlers to match cards for additional functionality
 * @param {HTMLElement} container - Container with match cards
 */
function addMatchCardInteractions(container) {
    if (!container) return;

    const matchCards = container.querySelectorAll('.match-card');

    matchCards.forEach(card => {
        card.addEventListener('click', function() {
            const matchId = this.getAttribute('data-match-id');
            if (matchId) {
                showMatchDetails(matchId);
            }
        });
    });
}

/**
 * Show detailed match information (could open a modal or navigate to details page)
 * @param {string} matchId - Match ID to show details for
 */
function showMatchDetails(matchId) {
    const match = getMatchById(matchId);
    if (!match) return;

    // For now, just log the match details
    // In a more complex app, this could open a modal or navigate to a details page
    console.log('Match details:', match);

    // You could implement a modal here:
    // openMatchModal(match);
}

/**
 * Update match cards in real-time (for live matches)
 * @param {HTMLElement} container - Container with match cards
 */
function updateLiveMatches(container) {
    if (!container) return;

    const liveMatches = getLiveMatches();

    liveMatches.forEach(match => {
        const card = container.querySelector(`[data-match-id="${match.id}"]`);
        if (card) {
            // Update the card with latest match data
            const newCard = createMatchCard(match);
            card.replaceWith(newCard);
        }
    });
}

// Export functions for use in other scripts
window.MatchCardComponent = {
    createMatchCard,
    renderMatchCards,
    addMatchCardInteractions,
    showMatchDetails,
    updateLiveMatches
};
