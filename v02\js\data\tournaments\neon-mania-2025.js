��/ * * 
   *   U N I F I E D   T O U R N A M E N T S   D A T A 
   * 
   *   T h i s   f i l e   c o n t a i n s   c o m p l e t e   t o u r n a m e n t   i n f o r m a t i o n   i n c l u d i n g   b r a c k e t   s t r u c t u r e , 
   *   d e t a i l e d   m a t c h   d a t a ,   s c o r e s ,   a n d   s c h e d u l i n g   -   a l l   i n   o n e   p l a c e   f o r   e a s y   m a n a g e m e n t . 
   * 
   *   T O   C R E A T E   A   N E W   T O U R N A M E N T : 
   *   1 .   A d d   a   n e w   t o u r n a m e n t   o b j e c t   t o   t h e   t o u r n a m e n t s   a r r a y   ( s e e   t e m p l a t e   b e l o w ) 
   *   2 .   U p d a t e   t h e   c u r r e n t T o u r n a m e n t   v a r i a b l e   t o   p o i n t   t o   y o u r   n e w   t o u r n a m e n t 
   *   3 .   M a k e   s u r e   a l l   t e a m   I D s   e x i s t   i n   t e a m s . j s 
   *   4 .   A l l   m a t c h   d a t a   i s   i n c l u d e d   i n   t h e   t o u r n a m e n t   s t r u c t u r e   -   n o   n e e d   f o r   s e p a r a t e   f i l e s ! 
   * 
   *   T O U R N A M E N T   S T R U C T U R E : 
   *   -   B a s i c   i n f o   ( n a m e ,   d a t e s ,   p r i z e s ,   e t c . ) 
   *   -   B r a c k e t   s t r u c t u r e   w i t h   e m b e d d e d   m a t c h   d e t a i l s 
   *   -   E a c h   m a t c h   i n c l u d e s :   t e a m s ,   s c o r e s ,   m a p s ,   t i m i n g ,   s t a t u s 
   *   -   R o u n d s   c a n   b e   c u s t o m i z e d   ( Q u a r t e r f i n a l s ,   S e m i f i n a l s ,   F i n a l ,   e t c . ) 
   * / 
 
 c o n s t   t o u r n a m e n t s   =   [ 
         / /   A d d   m o r e   t o u r n a m e n t s   h e r e 
       { 
               i d :   ' N e o n - M a n i a - 2 0 2 5 ' , 
               n a m e :   ' N e o n   M a n i a ' , 
               s u b t i t l e :   ' S u m m e r   2 0 2 5 ' , 
               d e s c r i p t i o n :   ' 8   t e a m s   c o m p e t e   i n   s i n g l e   e l i m i n a t i o n   f o r   c o m m u n i t y   g l o r y ' , 
               f o r m a t :   ' S i n g l e   E l i m i n a t i o n ' , 
               p r i z e P o o l :   ' � 5 0 0 0 ' , 
               s t a r t D a t e :   ' 2 0 2 5 - 0 6 - 0 1 ' , 
               e n d D a t e :   ' 2 0 2 5 - 0 6 - 0 7 ' , 
               s t a t u s :   ' a c t i v e ' ,   / /   a c t i v e ,   c o m p l e t e d ,   u p c o m i n g 
 
               / /   T o u r n a m e n t   s c h e d u l e   ( u s i n g   s i m p l e   t i m e   f o r m a t ) 
               s c h e d u l e :   { 
                       ' Q u a r t e r f i n a l s ' :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   1 5 ) ,           / /   3 : 0 0   P M   I S T 
                       ' S e m i f i n a l s ' :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 5 ' ,   ' 1 6 : 3 0 ' ) ,       / /   4 : 3 0   P M   I S T 
                       ' G r a n d   F i n a l ' :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 7 ' ,   1 7 )                 / /   5 : 0 0   P M   I S T 
               } , 
 
               / /   T o u r n a m e n t   r u l e s 
               r u l e s :   [ 
                       ' S t a n d a r d   V a l o r a n t   c o m p e t i t i v e   r u l e s ' , 
                       ' M a p   p i c k s   a n d   b a n s ' , 
                       ' S t a n d a r d   t i m e o u t   r u l e s   a p p l y ' 
               ] , 
 
               / /   P r i z e   d i s t r i b u t i o n 
               p r i z e s :   { 
                       ' 1 s t ' :   ' � 3 0 0 0 ' , 
                       ' 2 n d ' :   ' � 1 5 0 0 ' , 
                       ' 3 r d - 4 t h ' :   ' � 2 5 0   e a c h ' 
               } , 
 
               / /   T o u r n a m e n t   b r a c k e t   s t r u c t u r e   w i t h   e m b e d d e d   m a t c h   d e t a i l s 
               b r a c k e t :   { 
                       / /   Q u a r t e r f i n a l s   ( R o u n d   1 ) 
                       q u a r t e r f i n a l s :   [ 
                               { 
                                       i d :   ' q f 1 ' , 
                                       t e a m 1 :   ' p h o e n i x - r i s i n g ' , 
                                       t e a m 2 :   ' d i g i t a l - d e m o n s ' , 
                                       w i n n e r :   ' p h o e n i x - r i s i n g ' , 
                                       s t a t u s :   ' l i v e ' , 
                                       m a t c h D e t a i l s :   { 
                                               s c o r e :   {   t e a m 1 :   1 ,   t e a m 2 :   0   } , 
                                               r o u n d :   ' Q u a r t e r f i n a l s ' , 
                                               s c h e d u l e d T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   1 5 ) ,             / /   3 : 0 0   P M   I S T 
                                               a c t u a l S t a r t T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   1 5 ) , 
                                               e n d T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   1 6 ) , 
                                               b e s t O f :   1 , 
                                               s t r e a m L i n k :   ' h t t p s : / / w w w . y o u t u b e . c o m / w a t c h ? v = e x a m p l e ' ,     / /   A d d   s t r e a m   l i n k   f o r   l i v e   m a t c h e s 
                                               m a p s :   [ 
                                                       { 
                                                               n a m e :   ' B i n d ' , 
                                                               s c o r e :   {   t e a m 1 :   1 ,   t e a m 2 :   0   } , 
                                                               w i n n e r :   ' p h o e n i x - r i s i n g ' 
                                                       } 
                                               ] 
                                       } 
                               } , 
                               { 
                                       i d :   ' q f 2 ' , 
                                       t e a m 1 :   ' c y b e r - w o l v e s ' , 
                                       t e a m 2 :   ' c r i m s o n - t i d e ' , 
                                       w i n n e r :   ' c y b e r - w o l v e s ' , 
                                       s t a t u s :   ' c o m p l e t e d ' , 
                                       m a t c h D e t a i l s :   { 
                                               s c o r e :   {   t e a m 1 :   1 ,   t e a m 2 :   0   } , 
                                               r o u n d :   ' Q u a r t e r f i n a l s ' , 
                                               s c h e d u l e d T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   ' 1 6 ' ) ,   / /   3 : 3 0   P M   I S T 
                                               a c t u a l S t a r t T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   ' 1 6 ' ) , 
                                               e n d T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   ' 1 7 ' ) , 
                                               b e s t O f :   1 , 
                                               s t r e a m L i n k :   ' h t t p s : / / w w w . y o u t u b e . c o m / w a t c h ? v = e x a m p l e ' ,     / /   A d d   s t r e a m   l i n k   f o r   l i v e   m a t c h e s 
                                               m a p s :   [ 
                                                       { 
                                                               n a m e :   ' A s c e n t ' , 
                                                               s c o r e :   {   t e a m 1 :   1 3 ,   t e a m 2 :   5   } , 
                                                               w i n n e r :   ' c y b e r - w o l v e s ' 
                                                       } 
                                               ] 
                                       } 
                               } , 
                               { 
                                       i d :   ' q f 3 ' , 
                                       t e a m 1 :   ' n e o n - k n i g h t s ' , 
                                       t e a m 2 :   ' v o i d - h u n t e r s ' , 
                                       w i n n e r :   ' n e o n - k n i g h t s ' ,     / /   W i n n e r   i s   t h e   t e a m   t h a t   w o n   2   m a p s 
                                       s t a t u s :   ' c o m p l e t e d ' , 
                                       m a t c h D e t a i l s :   { 
                                               s c o r e :   {   t e a m 1 :   1 ,   t e a m 2 :   0   } ,     / /   O v e r a l l   s c o r e   ( m a p s   w o n   b y   e a c h   t e a m ) 
                                               r o u n d :   ' Q u a r t e r f i n a l s ' , 
                                               s c h e d u l e d T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   1 7 ) , 
                                               a c t u a l S t a r t T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   1 7 ) , 
                                               e n d T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   1 8 ) , 
                                               b e s t O f :   1 , 
                                               s t r e a m L i n k :   ' h t t p s : / / w w w . y o u t u b e . c o m / w a t c h ? v = e x a m p l e ' ,     / /   A d d   s t r e a m   l i n k   f o r   l i v e   m a t c h e s 
                                               m a p s :   [ 
                                                       { 
                                                               n a m e :   ' B i n d ' , 
                                                               s c o r e :   {   t e a m 1 :   1 3 ,   t e a m 2 :   7   } ,     / /   R o u n d   s c o r e   f o r   f i r s t   m a p 
                                                               w i n n e r :   ' n e o n - k n i g h t s ' 
                                                       } , 
                                               
                                               ] 
                                       } 
                               } , 
                               { 
                                       i d :   ' q f 4 ' , 
                                       t e a m 1 :   ' s h a d o w - s t r i k e ' , 
                                       t e a m 2 :   ' s t o r m - b r e a k e r s ' , 
                                       w i n n e r :   n u l l , 
                                       s t a t u s :   ' u p c o m i n g ' , 
                                       m a t c h D e t a i l s :   { 
                                               s c o r e :   {   t e a m 1 :   0 ,   t e a m 2 :   0   } , 
                                               r o u n d :   ' Q u a r t e r f i n a l s ' , 
                                               s c h e d u l e d T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 1 ' ,   ' 1 8 ' ) ,   / /   4 : 3 0   P M   I S T 
                                               a c t u a l S t a r t T i m e :   n u l l , 
                                               e n d T i m e :   n u l l , 
                                               b e s t O f :   3 , 
                                               s t r e a m L i n k :   ' h t t p s : / / w w w . y o u t u b e . c o m / w a t c h ? v = e x a m p l e ' ,     / /   A d d   s t r e a m   l i n k   f o r   l i v e   m a t c h e s 
                                               m a p s :   [ ] 
                                       } 
                               } 
                       ] , 
 
                       / /   S e m i f i n a l s   ( R o u n d   2 ) 
                       s e m i f i n a l s :   [ 
                               { 
                                       i d :   ' s f 1 ' , 
                                       t e a m 1 :   n u l l , 
                                       t e a m 2 :   n u l l , 
                                       w i n n e r :   n u l l , 
                                       s t a t u s :   ' u p c o m i n g ' , 
                                       m a t c h D e t a i l s :   { 
                                               s c o r e :   {   t e a m 1 :   0 ,   t e a m 2 :   0   } , 
                                               r o u n d :   ' S e m i f i n a l s ' , 
                                               s c h e d u l e d T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 5 ' ,   ' 1 6 : 3 0 ' ) ,   / /   4 : 3 0   P M   I S T 
                                               a c t u a l S t a r t T i m e :   n u l l , 
                                               e n d T i m e :   n u l l , 
                                               b e s t O f :   3 , 
                                               s t r e a m L i n k :   ' h t t p s : / / w w w . y o u t u b e . c o m / w a t c h ? v = e x a m p l e ' ,     / /   A d d   s t r e a m   l i n k   f o r   l i v e   m a t c h e s 
                                               m a p s :   [ ] 
                                       } 
                               } , 
                               { 
                                       i d :   ' s f 2 ' , 
                                       t e a m 1 :   n u l l , 
                                       t e a m 2 :   n u l l , 
                                       w i n n e r :   n u l l , 
                                       s t a t u s :   ' u p c o m i n g ' , 
                                       m a t c h D e t a i l s :   { 
                                               s c o r e :   {   t e a m 1 :   0 ,   t e a m 2 :   0   } , 
                                               r o u n d :   ' S e m i f i n a l s ' , 
                                               s c h e d u l e d T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 5 ' ,   1 8 ) ,             / /   6 : 0 0   P M   I S T 
                                               a c t u a l S t a r t T i m e :   n u l l , 
                                               e n d T i m e :   n u l l , 
                                               b e s t O f :   3 , 
                                               s t r e a m L i n k :   ' h t t p s : / / w w w . y o u t u b e . c o m / w a t c h ? v = e x a m p l e ' ,     / /   A d d   s t r e a m   l i n k   f o r   l i v e   m a t c h e s 
                                               m a p s :   [ ] 
                                       } 
                               } 
                       ] , 
 
                       / /   G r a n d   F i n a l   ( R o u n d   3 ) 
                       f i n a l :   { 
                               i d :   ' f i n a l ' , 
                               t e a m 1 :   n u l l , 
                               t e a m 2 :   n u l l , 
                               w i n n e r :   n u l l , 
                               s t a t u s :   ' u p c o m i n g ' , 
                               m a t c h D e t a i l s :   { 
                                       s c o r e :   {   t e a m 1 :   0 ,   t e a m 2 :   0   } , 
                                       r o u n d :   ' G r a n d   F i n a l ' , 
                                       s c h e d u l e d T i m e :   g e t I S T D a t e T i m e ( ' 2 0 2 5 - 0 6 - 0 7 ' ,   1 7 ) ,                   / /   5 : 0 0   P M   I S T 
                                       a c t u a l S t a r t T i m e :   n u l l , 
                                       e n d T i m e :   n u l l , 
                                       b e s t O f :   5 , 
                                       s t r e a m L i n k :   ' h t t p s : / / w w w . y o u t u b e . c o m / w a t c h ? v = e x a m p l e ' ,     / /   A d d   s t r e a m   l i n k   f o r   l i v e   m a t c h e s 
                                       m a p s :   [ ] 
                               } 
                       } 
               } , 
 
               / /   M a p   p o o l   f o r   t h e   t o u r n a m e n t 
               m a p P o o l :   [ 
                       ' A s c e n t ' , 
                       ' B i n d ' , 
                       ' H a v e n ' , 
                       ' S p l i t ' , 
                       ' I c e b o x ' , 
                       ' B r e e z e ' , 
                       ' F r a c t u r e ' 
               ] 
       } 
 ] ; 
 
 / /   C u r r e n t   a c t i v e   t o u r n a m e n t 
 c o n s t   c u r r e n t T o u r n a m e n t   =   t o u r n a m e n t s [ 0 ] ; 
 
 / * * 
 *   U T I L I T Y   F U N C T I O N S   F O R   T O U R N A M E N T S 
 * / 
 
 / /   G e t   c u r r e n t   t o u r n a m e n t 
 f u n c t i o n   g e t C u r r e n t T o u r n a m e n t ( )   { 
       r e t u r n   c u r r e n t T o u r n a m e n t ; 
 } 
 
 / /   G e t   t o u r n a m e n t   b y   I D 
 f u n c t i o n   g e t T o u r n a m e n t B y I d ( t o u r n a m e n t I d )   { 
       r e t u r n   t o u r n a m e n t s . f i n d ( t o u r n a m e n t   = >   t o u r n a m e n t . i d   = = =   t o u r n a m e n t I d ) ; 
 } 
 
 / /   G e t   t o u r n a m e n t   s t a t u s 
 f u n c t i o n   g e t T o u r n a m e n t S t a t u s ( )   { 
       r e t u r n   c u r r e n t T o u r n a m e n t . s t a t u s ; 
 } 
 
 / /   G e t   n e x t   m a t c h   i n   t h e   t o u r n a m e n t 
 f u n c t i o n   g e t N e x t M a t c h ( )   { 
       c o n s t   t o u r n a m e n t   =   g e t C u r r e n t T o u r n a m e n t ( ) ; 
 
       / /   C h e c k   s e m i f i n a l s   f i r s t 
       f o r   ( l e t   m a t c h   o f   t o u r n a m e n t . b r a c k e t . s e m i f i n a l s )   { 
               i f   ( m a t c h . s t a t u s   = = =   ' u p c o m i n g ' )   { 
                       r e t u r n   m a t c h ; 
               } 
       } 
 
       / /   C h e c k   f i n a l 
       i f   ( t o u r n a m e n t . b r a c k e t . f i n a l . s t a t u s   = = =   ' u p c o m i n g ' )   { 
               r e t u r n   t o u r n a m e n t . b r a c k e t . f i n a l ; 
       } 
 
       r e t u r n   n u l l ; 
 } 
 
 / /   G e t   c o m p l e t e d   m a t c h e s 
 f u n c t i o n   g e t C o m p l e t e d M a t c h e s ( )   { 
       c o n s t   t o u r n a m e n t   =   g e t C u r r e n t T o u r n a m e n t ( ) ; 
       c o n s t   c o m p l e t e d   =   [ ] ; 
 
       / /   A d d   c o m p l e t e d   q u a r t e r f i n a l s 
       t o u r n a m e n t . b r a c k e t . q u a r t e r f i n a l s . f o r E a c h ( m a t c h   = >   { 
               i f   ( m a t c h . s t a t u s   = = =   ' c o m p l e t e d ' )   { 
                       c o m p l e t e d . p u s h ( m a t c h ) ; 
               } 
       } ) ; 
 
       / /   A d d   c o m p l e t e d   s e m i f i n a l s 
       t o u r n a m e n t . b r a c k e t . s e m i f i n a l s . f o r E a c h ( m a t c h   = >   { 
               i f   ( m a t c h . s t a t u s   = = =   ' c o m p l e t e d ' )   { 
                       c o m p l e t e d . p u s h ( m a t c h ) ; 
               } 
       } ) ; 
 
       / /   A d d   c o m p l e t e d   f i n a l 
       i f   ( t o u r n a m e n t . b r a c k e t . f i n a l . s t a t u s   = = =   ' c o m p l e t e d ' )   { 
               c o m p l e t e d . p u s h ( t o u r n a m e n t . b r a c k e t . f i n a l ) ; 
       } 
 
       r e t u r n   c o m p l e t e d ; 
 } 
 
 / /   G e t   u p c o m i n g   m a t c h e s 
 f u n c t i o n   g e t U p c o m i n g M a t c h e s ( )   { 
       c o n s t   t o u r n a m e n t   =   g e t C u r r e n t T o u r n a m e n t ( ) ; 
       c o n s t   u p c o m i n g   =   [ ] ; 
 
       / /   A d d   u p c o m i n g   s e m i f i n a l s 
       t o u r n a m e n t . b r a c k e t . s e m i f i n a l s . f o r E a c h ( m a t c h   = >   { 
               i f   ( m a t c h . s t a t u s   = = =   ' u p c o m i n g ' )   { 
                       u p c o m i n g . p u s h ( m a t c h ) ; 
               } 
       } ) ; 
 
       / /   A d d   u p c o m i n g   f i n a l 
       i f   ( t o u r n a m e n t . b r a c k e t . f i n a l . s t a t u s   = = =   ' u p c o m i n g ' )   { 
               u p c o m i n g . p u s h ( t o u r n a m e n t . b r a c k e t . f i n a l ) ; 
       } 
 
       r e t u r n   u p c o m i n g ; 
 } 
 
 / /   U p d a t e   m a t c h   r e s u l t 
 f u n c t i o n   u p d a t e M a t c h R e s u l t ( m a t c h I d ,   w i n n e r I d )   { 
       c o n s t   t o u r n a m e n t   =   g e t C u r r e n t T o u r n a m e n t ( ) ; 
 
       / /   F i n d   a n d   u p d a t e   t h e   m a t c h 
       c o n s t   a l l M a t c h e s   =   [ 
               . . . t o u r n a m e n t . b r a c k e t . q u a r t e r f i n a l s , 
               . . . t o u r n a m e n t . b r a c k e t . s e m i f i n a l s , 
               t o u r n a m e n t . b r a c k e t . f i n a l 
       ] ; 
 
       c o n s t   m a t c h   =   a l l M a t c h e s . f i n d ( m   = >   m . i d   = = =   m a t c h I d ) ; 
       i f   ( m a t c h )   { 
               m a t c h . w i n n e r   =   w i n n e r I d ; 
               m a t c h . s t a t u s   =   ' c o m p l e t e d ' ; 
 
               / /   U p d a t e   t e a m   r e c o r d s 
               u p d a t e T e a m R e c o r d ( w i n n e r I d ,   t r u e ) ; 
               c o n s t   l o s e r I d   =   m a t c h . t e a m 1   = = =   w i n n e r I d   ?   m a t c h . t e a m 2   :   m a t c h . t e a m 1 ; 
               u p d a t e T e a m R e c o r d ( l o s e r I d ,   f a l s e ) ; 
               e l i m i n a t e T e a m ( l o s e r I d ) ; 
       } 
 } 
 
 / /   G e t   t o u r n a m e n t   p r o g r e s s   p e r c e n t a g e 
 f u n c t i o n   g e t T o u r n a m e n t P r o g r e s s ( )   { 
       c o n s t   t o u r n a m e n t   =   g e t C u r r e n t T o u r n a m e n t ( ) ; 
       c o n s t   t o t a l M a t c h e s   =   t o u r n a m e n t . b r a c k e t . q u a r t e r f i n a l s . l e n g t h   + 
                                               t o u r n a m e n t . b r a c k e t . s e m i f i n a l s . l e n g t h   +   1 ;   / /   + 1   f o r   f i n a l 
 
       c o n s t   c o m p l e t e d M a t c h e s   =   g e t C o m p l e t e d M a t c h e s ( ) . l e n g t h ; 
 
       r e t u r n   M a t h . r o u n d ( ( c o m p l e t e d M a t c h e s   /   t o t a l M a t c h e s )   *   1 0 0 ) ; 
 } 
 
 / * * 
 *   U N I F I E D   M A T C H   F U N C T I O N S 
 *   T h e s e   f u n c t i o n s   w o r k   w i t h   t h e   n e w   u n i f i e d   t o u r n a m e n t   s t r u c t u r e 
 * / 
 
 / /   G e t   a l l   m a t c h e s   f r o m   c u r r e n t   t o u r n a m e n t   ( f l a t t e n e d   f r o m   b r a c k e t   s t r u c t u r e ) 
 f u n c t i o n   g e t A l l M a t c h e s ( )   { 
       c o n s t   t o u r n a m e n t   =   g e t C u r r e n t T o u r n a m e n t ( ) ; 
       i f   ( ! t o u r n a m e n t )   r e t u r n   [ ] ; 
 
       c o n s t   a l l M a t c h e s   =   [ 
               . . . t o u r n a m e n t . b r a c k e t . q u a r t e r f i n a l s , 
               . . . t o u r n a m e n t . b r a c k e t . s e m i f i n a l s , 
               t o u r n a m e n t . b r a c k e t . f i n a l 
       ] ; 
 
       r e t u r n   a l l M a t c h e s ; 
 } 
 
 / /   G e t   m a t c h   b y   I D   ( w o r k s   w i t h   b r a c k e t   m a t c h   I D s ) 
 f u n c t i o n   g e t M a t c h B y I d ( m a t c h I d )   { 
       c o n s t   a l l M a t c h e s   =   g e t A l l M a t c h e s ( ) ; 
       r e t u r n   a l l M a t c h e s . f i n d ( m a t c h   = >   m a t c h . i d   = = =   m a t c h I d ) ; 
 } 
 
 / /   G e t   m a t c h e s   b y   s t a t u s 
 f u n c t i o n   g e t M a t c h e s B y S t a t u s ( s t a t u s )   { 
       c o n s t   a l l M a t c h e s   =   g e t A l l M a t c h e s ( ) ; 
       r e t u r n   a l l M a t c h e s . f i l t e r ( m a t c h   = >   m a t c h . s t a t u s   = = =   s t a t u s ) ; 
 } 
 
 / /   G e t   m a t c h e s   b y   r o u n d 
 f u n c t i o n   g e t M a t c h e s B y R o u n d ( r o u n d )   { 
       c o n s t   a l l M a t c h e s   =   g e t A l l M a t c h e s ( ) ; 
       r e t u r n   a l l M a t c h e s . f i l t e r ( m a t c h   = >   m a t c h . m a t c h D e t a i l s . r o u n d   = = =   r o u n d ) ; 
 } 
 
 / /   G e t   u p c o m i n g   m a t c h e s   ( n e x t   3 ) 
 f u n c t i o n   g e t U p c o m i n g M a t c h e s L i m i t ( l i m i t   =   3 )   { 
       c o n s t   a l l M a t c h e s   =   g e t A l l M a t c h e s ( ) ; 
       r e t u r n   a l l M a t c h e s 
               . f i l t e r ( m a t c h   = >   m a t c h . s t a t u s   = = =   ' u p c o m i n g ' ) 
               . s o r t ( ( a ,   b )   = >   n e w   D a t e ( a . m a t c h D e t a i l s . s c h e d u l e d T i m e )   -   n e w   D a t e ( b . m a t c h D e t a i l s . s c h e d u l e d T i m e ) ) 
               . s l i c e ( 0 ,   l i m i t ) ; 
 } 
 
 / /   G e t   r e c e n t   c o m p l e t e d   m a t c h e s   ( l a s t   3 ) 
 f u n c t i o n   g e t R e c e n t M a t c h e s ( l i m i t   =   3 )   { 
       c o n s t   a l l M a t c h e s   =   g e t A l l M a t c h e s ( ) ; 
       r e t u r n   a l l M a t c h e s 
               . f i l t e r ( m a t c h   = >   m a t c h . s t a t u s   = = =   ' c o m p l e t e d ' ) 
               . s o r t ( ( a ,   b )   = >   n e w   D a t e ( b . m a t c h D e t a i l s . e n d T i m e )   -   n e w   D a t e ( a . m a t c h D e t a i l s . e n d T i m e ) ) 
               . s l i c e ( 0 ,   l i m i t ) ; 
 } 
 
 / /   G e t   l i v e   m a t c h e s 
 f u n c t i o n   g e t L i v e M a t c h e s ( )   { 
       c o n s t   a l l M a t c h e s   =   g e t A l l M a t c h e s ( ) ; 
       r e t u r n   a l l M a t c h e s . f i l t e r ( m a t c h   = >   m a t c h . s t a t u s   = = =   ' l i v e ' ) ; 
 } 
 
 / /   F o r m a t   m a t c h   t i m e   f o r   d i s p l a y 
 f u n c t i o n   f o r m a t M a t c h T i m e ( t i m e s t a m p )   { 
       c o n s t   d a t e   =   n e w   D a t e ( t i m e s t a m p ) ; 
       c o n s t   o p t i o n s   =   { 
               m o n t h :   ' s h o r t ' , 
               d a y :   ' n u m e r i c ' , 
               h o u r :   ' 2 - d i g i t ' , 
               m i n u t e :   ' 2 - d i g i t ' , 
               t i m e Z o n e N a m e :   ' s h o r t ' 
       } ; 
       r e t u r n   d a t e . t o L o c a l e D a t e S t r i n g ( ' e n - U S ' ,   o p t i o n s ) ; 
 } 
 
 / /   G e t   m a t c h   d u r a t i o n   i n   m i n u t e s 
 f u n c t i o n   g e t M a t c h D u r a t i o n ( m a t c h )   { 
       i f   ( ! m a t c h . m a t c h D e t a i l s . a c t u a l S t a r t T i m e   | |   ! m a t c h . m a t c h D e t a i l s . e n d T i m e )   r e t u r n   n u l l ; 
 
       c o n s t   s t a r t   =   n e w   D a t e ( m a t c h . m a t c h D e t a i l s . a c t u a l S t a r t T i m e ) ; 
       c o n s t   e n d   =   n e w   D a t e ( m a t c h . m a t c h D e t a i l s . e n d T i m e ) ; 
       r e t u r n   M a t h . r o u n d ( ( e n d   -   s t a r t )   /   ( 1 0 0 0   *   6 0 ) ) ;   / /   C o n v e r t   t o   m i n u t e s 
 } 
 
 / /   G e t   m a t c h   s c o r e   s t r i n g   ( e . g . ,   " 2 - 1 " ) 
 f u n c t i o n   g e t M a t c h S c o r e S t r i n g ( m a t c h )   { 
       i f   ( m a t c h . s t a t u s   = = =   ' u p c o m i n g ' )   r e t u r n   ' v s ' ; 
       r e t u r n   ` $ { m a t c h . m a t c h D e t a i l s . s c o r e . t e a m 1 } - $ { m a t c h . m a t c h D e t a i l s . s c o r e . t e a m 2 } ` ; 
 } 
 
 / /   U p d a t e   m a t c h   r e s u l t   ( e n h a n c e d   v e r s i o n ) 
 f u n c t i o n   u p d a t e M a t c h R e s u l t D e t a i l e d ( m a t c h I d ,   w i n n e r I d ,   s c o r e ,   m a p s ,   e n d T i m e   =   n u l l )   { 
       c o n s t   m a t c h   =   g e t M a t c h B y I d ( m a t c h I d ) ; 
       i f   ( ! m a t c h )   r e t u r n   f a l s e ; 
 
       m a t c h . s t a t u s   =   ' c o m p l e t e d ' ; 
       m a t c h . m a t c h D e t a i l s . s c o r e   =   s c o r e ; 
       m a t c h . m a t c h D e t a i l s . m a p s   =   m a p s   | |   [ ] ; 
       m a t c h . m a t c h D e t a i l s . e n d T i m e   =   e n d T i m e   | |   n e w   D a t e ( ) . t o I S O S t r i n g ( ) ; 
 
       / /   F o r   b e s t - o f - 1   m a t c h e s ,   t h e   m a p   w i n n e r   i s   t h e   m a t c h   w i n n e r 
       i f   ( m a t c h . m a t c h D e t a i l s . b e s t O f   = = =   1   & &   m a p s   & &   m a p s . l e n g t h   = = =   1 )   { 
               m a t c h . w i n n e r   =   m a p s [ 0 ] . w i n n e r ; 
       }   e l s e   { 
               m a t c h . w i n n e r   =   w i n n e r I d ; 
       } 
 
       r e t u r n   t r u e ; 
 } 
 
 / /   S t a r t   a   m a t c h   ( s e t   a c t u a l   s t a r t   t i m e ) 
 f u n c t i o n   s t a r t M a t c h ( m a t c h I d )   { 
       c o n s t   m a t c h   =   g e t M a t c h B y I d ( m a t c h I d ) ; 
       i f   ( ! m a t c h )   r e t u r n   f a l s e ; 
 
       m a t c h . s t a t u s   =   ' l i v e ' ; 
       m a t c h . m a t c h D e t a i l s . a c t u a l S t a r t T i m e   =   n e w   D a t e ( ) . t o I S O S t r i n g ( ) ; 
 
       r e t u r n   t r u e ; 
 } 
 
 / * * 
 *   T I M E   U T I L I T Y   F U N C T I O N S   F O R   I N D I A N   T I M E   ( I S T ) 
 * / 
 
 / /   C o n v e r t   s i m p l e   t i m e   i n p u t   t o   f u l l   I S T   d a t e t i m e   s t r i n g 
 f u n c t i o n   g e t I S T D a t e T i m e ( d a t e ,   t i m e )   { 
       / /   E n s u r e   d a t e   i s   i n   Y Y Y Y - M M - D D   f o r m a t 
       c o n s t   d a t e S t r   =   d a t e . s p l i t ( ' T ' ) [ 0 ]   | |   d a t e ; 
       
       / /   H a n d l e   t i m e   i n p u t 
       l e t   h o u r s ,   m i n u t e s ; 
       i f   ( t y p e o f   t i m e   = = =   ' n u m b e r ' )   { 
               / /   I f   t i m e   i s   j u s t   a   n u m b e r   l i k e   1 5 ,   t r e a t   i t   a s   h o u r s 
               h o u r s   =   t i m e ; 
               m i n u t e s   =   0 ; 
       }   e l s e   i f   ( t y p e o f   t i m e   = = =   ' s t r i n g ' )   { 
               / /   I f   t i m e   i s   a   s t r i n g   l i k e   " 1 5 : 3 0 " ,   s p l i t   i n t o   h o u r s   a n d   m i n u t e s 
               c o n s t   [ h ,   m ]   =   t i m e . s p l i t ( ' : ' ) ; 
               h o u r s   =   p a r s e I n t ( h ) ; 
               m i n u t e s   =   p a r s e I n t ( m )   | |   0 ; 
       } 
 
       / /   F o r m a t   w i t h   I S T   t i m e z o n e 
       r e t u r n   ` $ { d a t e S t r } T $ { h o u r s . t o S t r i n g ( ) . p a d S t a r t ( 2 ,   ' 0 ' ) } : $ { m i n u t e s . t o S t r i n g ( ) . p a d S t a r t ( 2 ,   ' 0 ' ) } : 0 0 + 0 5 : 3 0 ` ; 
 } 
 
 / /   F o r m a t   m a t c h   t i m e   f o r   d i s p l a y   i n   I S T 
 f u n c t i o n   f o r m a t I S T T i m e ( t i m e s t a m p )   { 
       c o n s t   d a t e   =   n e w   D a t e ( t i m e s t a m p ) ; 
       c o n s t   o p t i o n s   =   { 
               t i m e Z o n e :   ' A s i a / K o l k a t a ' , 
               h o u r :   ' n u m e r i c ' , 
               m i n u t e :   ' 2 - d i g i t ' , 
               h o u r 1 2 :   t r u e , 
               m o n t h :   ' s h o r t ' , 
               d a y :   ' n u m e r i c ' , 
               y e a r :   ' n u m e r i c ' 
       } ; 
       r e t u r n   d a t e . t o L o c a l e S t r i n g ( ' e n - I N ' ,   o p t i o n s ) ; 
 } 
 
 / /   G e t   m a t c h   d u r a t i o n   i n   m i n u t e s 
 f u n c t i o n   g e t M a t c h D u r a t i o n ( m a t c h )   { 
       i f   ( ! m a t c h . m a t c h D e t a i l s . a c t u a l S t a r t T i m e   | |   ! m a t c h . m a t c h D e t a i l s . e n d T i m e )   r e t u r n   n u l l ; 
       c o n s t   s t a r t   =   n e w   D a t e ( m a t c h . m a t c h D e t a i l s . a c t u a l S t a r t T i m e ) ; 
       c o n s t   e n d   =   n e w   D a t e ( m a t c h . m a t c h D e t a i l s . e n d T i m e ) ; 
       r e t u r n   M a t h . r o u n d ( ( e n d   -   s t a r t )   /   ( 1 0 0 0   *   6 0 ) ) ; 
 } 
 
 / * * 
 *   E A S Y   T O U R N A M E N T   C R E A T I O N   E X A M P L E 
 * 
 *   T o   a d d   a   n e w   t o u r n a m e n t ,   c o p y   t h i s   t e m p l a t e   a n d   m o d i f y : 
 * 
 *   1 .   C o p y   t h e   " N e o n   M a n i a "   t o u r n a m e n t   s t r u c t u r e   a b o v e 
 *   2 .   C h a n g e   t h e   b a s i c   i n f o   ( i d ,   n a m e ,   d a t e s ,   e t c . ) 
 *   3 .   U p d a t e   t e a m   a s s i g n m e n t s   i n   q u a r t e r f i n a l s 
 *   4 .   A d j u s t   m a t c h   t i m e s   a s   n e e d e d 
 *   5 .   A d d   t o   t o u r n a m e n t s   a r r a y   a n d   u p d a t e   c u r r e n t T o u r n a m e n t ! 
 * 
 *   E v e r y t h i n g   i s   i n   o n e   p l a c e   -   n o   n e e d   t o   e d i t   m u l t i p l e   f i l e s ! 
 * / 
 
 / /   E x p o r t   f o r   u s e   i n   o t h e r   f i l e s   ( i f   u s i n g   m o d u l e s ) 
 / /   e x p o r t   {   t o u r n a m e n t s ,   c u r r e n t T o u r n a m e n t ,   g e t C u r r e n t T o u r n a m e n t ,   g e t T o u r n a m e n t B y I d ,   g e t N e x t M a t c h ,   g e t C o m p l e t e d M a t c h e s ,   g e t U p c o m i n g M a t c h e s ,   u p d a t e M a t c h R e s u l t ,   g e t T o u r n a m e n t P r o g r e s s ,   g e t A l l M a t c h e s ,   g e t M a t c h B y I d ,   g e t M a t c h e s B y S t a t u s ,   g e t M a t c h e s B y R o u n d ,   g e t U p c o m i n g M a t c h e s L i m i t ,   g e t R e c e n t M a t c h e s ,   g e t L i v e M a t c h e s ,   f o r m a t M a t c h T i m e ,   g e t M a t c h D u r a t i o n ,   g e t M a t c h S c o r e S t r i n g ,   u p d a t e M a t c h R e s u l t D e t a i l e d ,   s t a r t M a t c h   } ; 
 
 / /   E x p o r t   f u n c t i o n s   f o r   u s e   i n   o t h e r   f i l e s 
 w i n d o w . T o u r n a m e n t T i m e   =   { 
       f o r m a t I S T T i m e , 
       g e t M a t c h D u r a t i o n , 
       g e t I S T D a t e T i m e 
 } ; 
 