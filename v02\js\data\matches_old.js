/**
 * MATCHES DATA
 * 
 * This file contains detailed match information including scores, maps, and timestamps.
 * Each match corresponds to a bracket match in tournaments.js but contains more detailed info.
 * 
 * TO ADD A NEW MATCH RESULT:
 * 1. Find the match object by bracketMatchId
 * 2. Update the score, maps, and status
 * 3. Make sure the winner matches the bracket data
 * 4. Update timestamps as needed
 */

const matches = [
    // QUARTERFINALS - COMPLETED
    {
        id: 'match-qf1',
        bracketMatchId: 'qf1',
        team1: 'phoenix-rising',
        team2: 'digital-demons',
        score: { team1: 2, team2: 0 }, // Best of 3
        winner: 'phoenix-rising',
        status: 'completed',
        round: 'Quarterfinals',
        scheduledTime: '2024-03-15T18:00:00Z',
        actualStartTime: '2024-03-15T18:05:00Z',
        endTime: '2024-03-15T19:45:00Z',
        maps: [
            {
                name: 'Ascent',
                score: { team1: 13, team2: 8 },
                winner: 'phoenix-rising'
            },
            {
                name: 'Bind',
                score: { team1: 13, team2: 11 },
                winner: 'phoenix-rising'
            }
        ]
    },
    {
        id: 'match-qf2',
        bracketMatchId: 'qf2',
        team1: 'cyber-wolves',
        team2: 'crimson-tide',
        score: { team1: 2, team2: 1 },
        winner: 'cyber-wolves',
        status: 'completed',
        round: 'Quarterfinals',
        scheduledTime: '2024-03-15T19:00:00Z',
        actualStartTime: '2024-03-15T19:10:00Z',
        endTime: '2024-03-15T21:30:00Z',
        maps: [
            {
                name: 'Haven',
                score: { team1: 13, team2: 10 },
                winner: 'cyber-wolves'
            },
            {
                name: 'Split',
                score: { team1: 11, team2: 13 },
                winner: 'crimson-tide'
            },
            {
                name: 'Icebox',
                score: { team1: 13, team2: 7 },
                winner: 'cyber-wolves'
            }
        ]
    },
    {
        id: 'match-qf3',
        bracketMatchId: 'qf3',
        team1: 'neon-knights',
        team2: 'void-hunters',
        score: { team1: 2, team2: 1 },
        winner: 'neon-knights',
        status: 'completed',
        round: 'Quarterfinals',
        scheduledTime: '2024-03-15T20:00:00Z',
        actualStartTime: '2024-03-15T20:15:00Z',
        endTime: '2024-03-15T22:00:00Z',
        maps: [
            {
                name: 'Breeze',
                score: { team1: 13, team2: 9 },
                winner: 'neon-knights'
            },
            {
                name: 'Fracture',
                score: { team1: 10, team2: 13 },
                winner: 'void-hunters'
            },
            {
                name: 'Ascent',
                score: { team1: 13, team2: 11 },
                winner: 'neon-knights'
            }
        ]
    },
    {
        id: 'match-qf4',
        bracketMatchId: 'qf4',
        team1: 'shadow-strike',
        team2: 'storm-breakers',
        score: { team1: 2, team2: 0 },
        winner: 'shadow-strike',
        status: 'completed',
        round: 'Quarterfinals',
        scheduledTime: '2024-03-15T21:00:00Z',
        actualStartTime: '2024-03-15T21:05:00Z',
        endTime: '2024-03-15T22:30:00Z',
        maps: [
            {
                name: 'Bind',
                score: { team1: 13, team2: 6 },
                winner: 'shadow-strike'
            },
            {
                name: 'Haven',
                score: { team1: 13, team2: 9 },
                winner: 'shadow-strike'
            }
        ]
    },
    
    // SEMIFINALS - ONE COMPLETED, ONE UPCOMING
    {
        id: 'match-sf1',
        bracketMatchId: 'sf1',
        team1: 'phoenix-rising',
        team2: 'cyber-wolves',
        score: { team1: 2, team2: 1 },
        winner: 'phoenix-rising',
        status: 'completed',
        round: 'Semifinals',
        scheduledTime: '2024-03-16T19:00:00Z',
        actualStartTime: '2024-03-16T19:10:00Z',
        endTime: '2024-03-16T21:45:00Z',
        maps: [
            {
                name: 'Ascent',
                score: { team1: 13, team2: 11 },
                winner: 'phoenix-rising'
            },
            {
                name: 'Split',
                score: { team1: 9, team2: 13 },
                winner: 'cyber-wolves'
            },
            {
                name: 'Icebox',
                score: { team1: 13, team2: 8 },
                winner: 'phoenix-rising'
            }
        ]
    },
    {
        id: 'match-sf2',
        bracketMatchId: 'sf2',
        team1: 'neon-knights',
        team2: 'shadow-strike',
        score: { team1: 0, team2: 0 },
        winner: null,
        status: 'upcoming',
        round: 'Semifinals',
        scheduledTime: '2024-03-16T20:30:00Z',
        actualStartTime: null,
        endTime: null,
        maps: []
    },
    
    // GRAND FINAL - UPCOMING
    {
        id: 'match-final',
        bracketMatchId: 'final',
        team1: 'phoenix-rising',
        team2: null, // Winner of sf2
        score: { team1: 0, team2: 0 },
        winner: null,
        status: 'upcoming',
        round: 'Grand Final',
        scheduledTime: '2024-03-17T20:00:00Z',
        actualStartTime: null,
        endTime: null,
        maps: [],
        bestOf: 5 // Grand Final is Best of 5
    }
];

/**
 * UTILITY FUNCTIONS FOR MATCHES
 */

// Get match by ID
function getMatchById(matchId) {
    return matches.find(match => match.id === matchId);
}

// Get match by bracket match ID
function getMatchByBracketId(bracketMatchId) {
    return matches.find(match => match.bracketMatchId === bracketMatchId);
}

// Get matches by status
function getMatchesByStatus(status) {
    return matches.filter(match => match.status === status);
}

// Get matches by round
function getMatchesByRound(round) {
    return matches.filter(match => match.round === round);
}

// Get upcoming matches (next 3)
function getUpcomingMatchesLimit(limit = 3) {
    return matches
        .filter(match => match.status === 'upcoming')
        .sort((a, b) => new Date(a.scheduledTime) - new Date(b.scheduledTime))
        .slice(0, limit);
}

// Get recent completed matches (last 3)
function getRecentMatches(limit = 3) {
    return matches
        .filter(match => match.status === 'completed')
        .sort((a, b) => new Date(b.endTime) - new Date(a.endTime))
        .slice(0, limit);
}

// Format match time for display
function formatMatchTime(timestamp) {
    const date = new Date(timestamp);
    const options = {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
    };
    return date.toLocaleDateString('en-US', options);
}

// Get match duration in minutes
function getMatchDuration(match) {
    if (!match.actualStartTime || !match.endTime) return null;
    
    const start = new Date(match.actualStartTime);
    const end = new Date(match.endTime);
    return Math.round((end - start) / (1000 * 60)); // Convert to minutes
}

// Update match result
function updateMatchResult(matchId, winnerId, score, maps) {
    const match = getMatchById(matchId);
    if (!match) return false;
    
    match.winner = winnerId;
    match.score = score;
    match.maps = maps || [];
    match.status = 'completed';
    match.endTime = new Date().toISOString();
    
    return true;
}

// Start a match (set actual start time)
function startMatch(matchId) {
    const match = getMatchById(matchId);
    if (!match) return false;
    
    match.status = 'live';
    match.actualStartTime = new Date().toISOString();
    
    return true;
}

// Get live matches
function getLiveMatches() {
    return matches.filter(match => match.status === 'live');
}

// Get match score string (e.g., "2-1")
function getMatchScoreString(match) {
    if (match.status === 'upcoming') return 'vs';
    return `${match.score.team1}-${match.score.team2}`;
}

// Export for use in other files (if using modules)
// export { matches, getMatchById, getMatchByBracketId, getMatchesByStatus, getMatchesByRound, getUpcomingMatchesLimit, getRecentMatches, formatMatchTime, getMatchDuration, updateMatchResult, startMatch, getLiveMatches, getMatchScoreString };
