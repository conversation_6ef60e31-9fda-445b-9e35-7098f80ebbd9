/**
 * NEON MANIA - SUMMER 2025 TOURNAMENT
 * 
 * This file contains all data for the Neon Mania tournament.
 * Everything is self-contained: tournament info, bracket, matches, scores.
 */

const neonMania2025 = {
    id: 'neon-mania-2025',
    name: 'Neon Mania',
    subtitle: 'Summer 2025',
    description: '8 teams compete in single elimination for community glory',
    format: 'Single Elimination',
    prizePool: '₹5000',
    startDate: '2025-06-01',
    endDate: '2025-06-07',
    status: 'active',

    // Tournament schedule
    schedule: {
        'Quarterfinals': '2025-06-01',
        'Semifinals': '2025-06-05',
        'Grand Final': '2025-06-07'
    },

    // Tournament rules
    rules: [
        'Standard Valorant competitive rules',
        'Map picks and bans',
        'Standard timeout rules apply'
    ],

    // Prize distribution
    prizes: {
        '1st': '₹3000',
        '2nd': '₹1500',
        '3rd-4th': '₹250 each'
    },

    // Tournament bracket structure with embedded match details
    bracket: {
        // Quarterfinals (Round 1) - June 1, 2025
        quarterfinals: [
            {
                id: 'qf1',
                team1: 'phoenix-rising',
                team2: 'digital-demons',
                winner: null,
                status: 'upcoming',
                matchDetails: {
                    score: { team1: 0, team2: 0 },
                    round: 'Quarterfinals',
                    scheduledTime: '2025-06-01T14:00:00Z',
                    actualStartTime: null,
                    endTime: null,
                    bestOf: 3,
                    maps: []
                }
            },
            {
                id: 'qf2',
                team1: 'cyber-wolves',
                team2: 'crimson-tide',
                winner: null,
                status: 'upcoming',
                matchDetails: {
                    score: { team1: 0, team2: 0 },
                    round: 'Quarterfinals',
                    scheduledTime: '2025-06-01T16:00:00Z',
                    actualStartTime: null,
                    endTime: null,
                    bestOf: 3,
                    maps: []
                }
            },
            {
                id: 'qf3',
                team1: 'neon-knights',
                team2: 'void-hunters',
                winner: null,
                status: 'upcoming',
                matchDetails: {
                    score: { team1: 0, team2: 0 },
                    round: 'Quarterfinals',
                    scheduledTime: '2025-06-01T18:00:00Z',
                    actualStartTime: null,
                    endTime: null,
                    bestOf: 3,
                    maps: []
                }
            },
            {
                id: 'qf4',
                team1: 'shadow-strike',
                team2: 'storm-breakers',
                winner: null,
                status: 'upcoming',
                matchDetails: {
                    score: { team1: 0, team2: 0 },
                    round: 'Quarterfinals',
                    scheduledTime: '2025-06-01T20:00:00Z',
                    actualStartTime: null,
                    endTime: null,
                    bestOf: 3,
                    maps: []
                }
            }
        ],

        // Semifinals (Round 2) - June 5, 2025
        semifinals: [
            {
                id: 'sf1',
                team1: null, // Winner of qf1
                team2: null, // Winner of qf2
                winner: null,
                status: 'upcoming',
                matchDetails: {
                    score: { team1: 0, team2: 0 },
                    round: 'Semifinals',
                    scheduledTime: '2025-06-05T16:00:00Z',
                    actualStartTime: null,
                    endTime: null,
                    bestOf: 3,
                    maps: []
                }
            },
            {
                id: 'sf2',
                team1: null, // Winner of qf3
                team2: null, // Winner of qf4
                winner: null,
                status: 'upcoming',
                matchDetails: {
                    score: { team1: 0, team2: 0 },
                    round: 'Semifinals',
                    scheduledTime: '2025-06-05T18:00:00Z',
                    actualStartTime: null,
                    endTime: null,
                    bestOf: 3,
                    maps: []
                }
            }
        ],

        // Grand Final (Round 3) - June 7, 2025
        final: {
            id: 'final',
            team1: null, // Winner of sf1
            team2: null, // Winner of sf2
            winner: null,
            status: 'upcoming',
            matchDetails: {
                score: { team1: 0, team2: 0 },
                round: 'Grand Final',
                scheduledTime: '2025-06-07T18:00:00Z',
                actualStartTime: null,
                endTime: null,
                bestOf: 5, // Grand Final is Best of 5
                maps: []
            }
        }
    },

    // Map pool for the tournament
    mapPool: [
        'Ascent',
        'Bind',
        'Haven',
        'Split',
        'Icebox',
        'Breeze',
        'Fracture'
    ]
};