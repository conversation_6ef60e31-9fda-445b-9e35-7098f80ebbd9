/**
 * SCHEDULE PAGE FUNCTIONALITY
 *
 * Handles the schedule page display including match filtering,
 * detailed results, and tournament timeline.
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeSchedulePage();
});

function initializeSchedulePage() {
    loadScheduleTable();
    loadDetailedMatches();
    loadTournamentTimeline();
    setupScheduleFilters();
    setupTabNavigation();
    updateTournamentProgress();
}

/**
 * Load and display the schedule table
 */
function loadScheduleTable() {
    const tbody = document.getElementById('schedule-tbody');
    if (!tbody) {
        console.error('Schedule table body not found');
        return;
    }

    // Sort matches by scheduled time
    const sortedMatches = [...getAllMatches()].sort((a, b) =>
        new Date(a.matchDetails.scheduledTime) - new Date(b.matchDetails.scheduledTime)
    );

    renderScheduleRows(sortedMatches, tbody);
}

/**
 * Render schedule table rows
 * @param {Array} matchesToRender - Array of matches to display
 * @param {HTMLElement} tbody - Table body element
 */
function renderScheduleRows(matchesToRender, tbody) {
    tbody.innerHTML = '';

    if (matchesToRender.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="no-matches">No matches found</td>
            </tr>
        `;
        return;
    }

    matchesToRender.forEach(match => {
        const row = createScheduleRow(match);
        tbody.appendChild(row);
    });
}

/**
 * Create a schedule table row
 * @param {Object} match - Match data object
 * @returns {HTMLElement} - Table row element
 */
function createScheduleRow(match) {
    const row = document.createElement('tr');
    row.className = `schedule-row ${match.status}`;
    row.setAttribute('data-match-id', match.id);

    const team1 = getTeamById(match.team1);
    const team2 = getTeamById(match.team2);

    if (!team1 || !team2) {
        return createPlaceholderRow();
    }

    const dateTime = TournamentTime.formatISTTime(match.matchDetails.scheduledTime);
    const score = match.status === 'completed' ? 
        `${match.matchDetails.score.team1}-${match.matchDetails.score.team2}` : 
        '-';
    const duration = TournamentTime.getMatchDuration(match);
    const durationText = duration ? `${duration} min` : '-';

    // Get winner team name for the winner column
    let winnerTeam = '-';
    if (match.status === 'completed' && match.winner) {
        const winner = getTeamById(match.winner);
        winnerTeam = winner ? winner.name : '-';
    }

    // Create status cell with clickable link for live matches
    let statusHtml = '';
    if (match.status === 'live' && match.matchDetails.streamLink) {
        statusHtml = `<a href="${match.matchDetails.streamLink}" target="_blank" class="match-status live" title="Click to watch stream">LIVE</a>`;
    } else {
        statusHtml = `<span class="match-status ${match.status}">${match.status}</span>`;
    }

    row.innerHTML = `
        <td class="date-time">${dateTime}</td>
        <td class="round">${match.matchDetails.round}</td>
        <td class="teams">
            <span class="team">${team1.name}</span>
            <span class="vs">vs</span>
            <span class="team">${team2.name}</span>
        </td>
        <td class="score">${score}</td>
        <td>${statusHtml}</td>
        <td class="duration">${durationText}</td>
        <td class="winner-cell">${winnerTeam}</td>
    `;

    // Add click handler for row details
    row.addEventListener('click', () => showMatchDetails(match.id));

    return row;
}

/**
 * Create placeholder row for missing data
 * @returns {HTMLElement} - Placeholder table row
 */
function createPlaceholderRow() {
    const row = document.createElement('tr');
    row.className = 'schedule-row placeholder';
    row.innerHTML = `
        <td>TBD</td>
        <td>TBD</td>
        <td>TBD vs TBD</td>
        <td>-</td>
        <td><span class="match-status upcoming">TBD</span></td>
        <td>-</td>
        <td>-</td>
    `;
    return row;
}

/**
 * Load detailed match results
 */
function loadDetailedMatches() {
    const container = document.getElementById('detailed-matches');
    if (!container) {
        console.error('Detailed matches container not found');
        return;
    }

    // Get completed matches with detailed results
    const completedMatches = getMatchesByStatus('completed');

    if (completedMatches.length === 0) {
        container.innerHTML = `
            <div class="no-matches">
                <p>No completed matches with detailed results yet.</p>
            </div>
        `;
        return;
    }

    // Render detailed match cards
    renderMatchCards(completedMatches, container, true);
}

/**
 * Load tournament timeline
 */
function loadTournamentTimeline() {
    const container = document.getElementById('tournament-timeline');
    if (!container) {
        console.error('Tournament timeline container not found');
        return;
    }

    const tournament = getCurrentTournament();
    const timeline = createTournamentTimeline(tournament);
    container.appendChild(timeline);
}

/**
 * Create tournament timeline
 * @param {Object} tournament - Tournament data
 * @returns {HTMLElement} - Timeline element
 */
function createTournamentTimeline(tournament) {
    const timeline = document.createElement('div');
    timeline.className = 'timeline-container';

    const timelineItems = [
        {
            date: tournament.startDate,
            title: 'Tournament Begins',
            description: 'Quarterfinals matches start',
            status: 'completed'
        },
        {
            date: '2024-03-16',
            title: 'Semifinals',
            description: 'Top 4 teams compete',
            status: 'active'
        },
        {
            date: tournament.endDate,
            title: 'Grand Final',
            description: 'Championship match',
            status: 'upcoming'
        }
    ];

    timelineItems.forEach((item, index) => {
        const timelineItem = document.createElement('div');
        timelineItem.className = `timeline-item ${item.status}`;

        timelineItem.innerHTML = `
            <div class="timeline-marker"></div>
            <div class="timeline-content">
                <div class="timeline-date">${formatTimelineDate(item.date)}</div>
                <h3 class="timeline-title">${item.title}</h3>
                <p class="timeline-description">${item.description}</p>
            </div>
        `;

        timeline.appendChild(timelineItem);
    });

    return timeline;
}

/**
 * Format date for timeline display
 * @param {string} dateString - Date string
 * @returns {string} - Formatted date
 */
function formatTimelineDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
    });
}

/**
 * Setup schedule filtering functionality
 */
function setupScheduleFilters() {
    const roundFilter = document.getElementById('round-filter');
    const statusFilter = document.getElementById('status-filter');
    const dateFilter = document.getElementById('date-filter');
    const tbody = document.getElementById('schedule-tbody');

    if (!roundFilter || !statusFilter || !dateFilter || !tbody) {
        console.error('Filter elements not found');
        return;
    }

    // Add event listeners to all filters
    [roundFilter, statusFilter, dateFilter].forEach(filter => {
        filter.addEventListener('change', () => applyScheduleFilters());
    });
}

/**
 * Setup tab navigation functionality
 */
function setupTabNavigation() {
    const navTabs = document.querySelectorAll('.nav-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all tabs and contents
            navTabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Show corresponding content
            const targetContent = document.getElementById(`${targetTab}-tab`);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });
}

/**
 * Update tournament progress
 */
function updateTournamentProgress() {
    const progressElement = document.getElementById('tournament-progress');
    const progressFill = document.getElementById('progress-fill');

    if (progressElement && progressFill) {
        const progress = getTournamentProgress();
        progressElement.textContent = `${progress}%`;
        progressFill.style.width = `${progress}%`;
    }
}

/**
 * Apply all schedule filters
 */
function applyScheduleFilters() {
    const roundFilter = document.getElementById('round-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    const dateFilter = document.getElementById('date-filter').value;
    const tbody = document.getElementById('schedule-tbody');

    let filteredMatches = [...getAllMatches()];

    // Apply round filter
    if (roundFilter !== 'all') {
        filteredMatches = filteredMatches.filter(match => match.matchDetails.round === roundFilter);
    }

    // Apply status filter
    if (statusFilter !== 'all') {
        filteredMatches = filteredMatches.filter(match => match.status === statusFilter);
    }

    // Apply date filter
    if (dateFilter !== 'all') {
        filteredMatches = filteredMatches.filter(match => {
            const matchDate = new Date(match.matchDetails.scheduledTime).toISOString().split('T')[0];
            return matchDate === dateFilter;
        });
    }

    // Sort by scheduled time
    filteredMatches.sort((a, b) => new Date(a.matchDetails.scheduledTime) - new Date(b.matchDetails.scheduledTime));

    // Render filtered results
    renderScheduleRows(filteredMatches, tbody);
}

/**
 * Clear all filters
 */
function clearFilters() {
    document.getElementById('round-filter').value = 'all';
    document.getElementById('status-filter').value = 'all';
    document.getElementById('date-filter').value = 'all';
    applyScheduleFilters();
}

/**
 * Export schedule data
 */
function exportScheduleData() {
    const scheduleData = {
        tournament: getCurrentTournament(),
        matches: getAllMatches(),
        exportDate: new Date().toISOString()
    };

    const dataStr = JSON.stringify(scheduleData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'tournament-schedule.json';
    link.click();
}

/**
 * Print schedule
 */
function printSchedule() {
    const printWindow = window.open('', '_blank');
    const tournament = getCurrentTournament();

    printWindow.document.write(`
        <html>
        <head>
            <title>${tournament.name} ${tournament.subtitle} - Schedule</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .completed { background-color: #e8f5e8; }
                .upcoming { background-color: #fff3cd; }
                .live { background-color: #f8d7da; }
            </style>
        </head>
        <body>
            <h1>${tournament.name} ${tournament.subtitle}</h1>
            <h2>Tournament Schedule</h2>
            ${document.querySelector('.schedule-table').outerHTML}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

// Make functions available globally
window.clearFilters = clearFilters;
window.exportSchedule = exportScheduleData;

// Export functions for use in other scripts
window.SchedulePage = {
    initializeSchedulePage,
    loadScheduleTable,
    applyScheduleFilters,
    clearFilters,
    exportScheduleData,
    printSchedule
};
