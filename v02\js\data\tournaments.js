/**
 * UNIFIED TOURNAMENTS DATA
 *
 * This file contains complete tournament information including bracket structure,
 * detailed match data, scores, and scheduling - all in one place for easy management.
 *
 * TO CREATE A NEW TOURNAMENT:
 * 1. Add a new tournament object to the tournaments array (see template below)
 * 2. Update the currentTournament variable to point to your new tournament
 * 3. Make sure all team IDs exist in teams.js
 * 4. All match data is included in the tournament structure - no need for separate files!
 *
 * TOURNAMENT STRUCTURE:
 * - Basic info (name, dates, prizes, etc.)
 * - Bracket structure with embedded match details
 * - Each match includes: teams, scores, maps, timing, status
 * - Rounds can be customized (Quarterfinals, Semifinals, Final, etc.)
 */

const tournaments = [
     // Add more tournaments here
    {
        id: 'Neon-Mania-2025',
        name: 'Neon Mania',
        subtitle: 'Summer 2025',
        description: '8 teams compete in single elimination for community glory',
        format: 'Single Elimination',
        prizePool: '₹5000',
        startDate: '2025-06-01',
        endDate: '2025-06-07',
        status: 'active', // active, completed, upcoming

        // Tournament schedule (using simple time format)
        schedule: {
            'Quarterfinals': getISTDateTime('2025-06-01', 15),     // 3:00 PM IST
            'Semifinals': getISTDateTime('2025-06-05', '16:30'),   // 4:30 PM IST
            'Grand Final': getISTDateTime('2025-06-07', 17)        // 5:00 PM IST
        },

        // Tournament rules
        rules: [
            'Standard Valorant competitive rules',
            'Map picks and bans',
            'Standard timeout rules apply'
        ],

        // Prize distribution
        prizes: {
            '1st': '₹3000',
            '2nd': '₹1500',
            '3rd-4th': '₹250 each'
        },

        // Tournament bracket structure with embedded match details
        bracket: {
            // Quarterfinals (Round 1)
            quarterfinals: [
                {
                    id: 'qf1',
                    team1: 'phoenix-rising',
                    team2: 'digital-demons',
                    winner: 'phoenix-rising',
                    status: 'completed',
                    matchDetails: {
                        score: { team1: 1, team2: 0 },
                        round: 'Quarterfinals',
                        scheduledTime: getISTDateTime('2025-06-01', 15),      // 3:00 PM IST
                        actualStartTime: getISTDateTime('2025-06-01', 15),
                        endTime: getISTDateTime('2025-06-01', 16),
                        bestOf: 1,
                        streamLink: 'https://www.youtube.com/watch?v=example',  // Add stream link for live matches
                        maps: [
                            {
                                name: 'Bind',
                                score: { team1: 1, team2: 0 },
                                winner: 'phoenix-rising'
                            }
                        ]
                    }
                },
                {
                    id: 'qf2',
                    team1: 'cyber-wolves',
                    team2: 'crimson-tide',
                    winner: 'cyber-wolves',
                    status: 'upcoming',
                    matchDetails: {
                        score: { team1: 1, team2: 0 },
                        round: 'Quarterfinals',
                        scheduledTime: getISTDateTime('2025-06-01', '16'), // 3:30 PM IST
                        actualStartTime: getISTDateTime('2025-06-01', '16'),
                        endTime: getISTDateTime('2025-06-01', '17'),
                        bestOf: 1,
                        streamLink: 'https://www.youtube.com/watch?v=example',  // Add stream link for live matches
                        maps: [
                            {
                                name: 'Ascent',
                                score: { team1: 13, team2: 5 },
                                winner: 'cyber-wolves'
                            }
                        ]
                    }
                },
                {
                    id: 'qf3',
                    team1: 'neon-knights',
                    team2: 'void-hunters',
                    winner: 'neon-knights',  // Winner is the team that won 2 maps
                    status: 'upcoming',
                    matchDetails: {
                        score: { team1: 1, team2: 0 },  // Overall score (maps won by each team)
                        round: 'Quarterfinals',
                        scheduledTime: getISTDateTime('2025-06-01', 17),
                        actualStartTime: getISTDateTime('2025-06-01', 17),
                        endTime: getISTDateTime('2025-06-01', 18),
                        bestOf: 1,
                        streamLink: 'https://www.youtube.com/watch?v=example',  // Add stream link for live matches
                        maps: [
                            {
                                name: 'Bind',
                                score: { team1: 13, team2: 7 },  // Round score for first map
                                winner: 'neon-knights'
                            },
                        
                        ]
                    }
                },
                {
                    id: 'qf4',
                    team1: 'shadow-strike',
                    team2: 'storm-breakers',
                    winner: null,
                    status: 'upcoming',
                    matchDetails: {
                        score: { team1: 0, team2: 0 },
                        round: 'Quarterfinals',
                        scheduledTime: getISTDateTime('2025-06-01', '18'), // 4:30 PM IST
                        actualStartTime: null,
                        endTime: null,
                        bestOf: 3,
                        streamLink: 'https://www.youtube.com/watch?v=example',  // Add stream link for live matches
                        maps: []
                    }
                }
            ],

            // Semifinals (Round 2)
            semifinals: [
                {
                    id: 'sf1',
                    team1: null,
                    team2: null,
                    winner: null,
                    status: 'upcoming',
                    matchDetails: {
                        score: { team1: 0, team2: 0 },
                        round: 'Semifinals',
                        scheduledTime: getISTDateTime('2025-06-05', '16:30'), // 4:30 PM IST
                        actualStartTime: null,
                        endTime: null,
                        bestOf: 3,
                        streamLink: 'https://www.youtube.com/watch?v=example',  // Add stream link for live matches
                        maps: []
                    }
                },
                {
                    id: 'sf2',
                    team1: null,
                    team2: null,
                    winner: null,
                    status: 'upcoming',
                    matchDetails: {
                        score: { team1: 0, team2: 0 },
                        round: 'Semifinals',
                        scheduledTime: getISTDateTime('2025-06-05', 18),      // 6:00 PM IST
                        actualStartTime: null,
                        endTime: null,
                        bestOf: 3,
                        streamLink: 'https://www.youtube.com/watch?v=example',  // Add stream link for live matches
                        maps: []
                    }
                }
            ],

            // Grand Final (Round 3)
            final: {
                id: 'final',
                team1: null,
                team2: null,
                winner: null,
                status: 'upcoming',
                matchDetails: {
                    score: { team1: 0, team2: 0 },
                    round: 'Grand Final',
                    scheduledTime: getISTDateTime('2025-06-07', 17),         // 5:00 PM IST
                    actualStartTime: null,
                    endTime: null,
                    bestOf: 5,
                    streamLink: 'https://www.youtube.com/watch?v=example',  // Add stream link for live matches
                    maps: []
                }
            }
        },

        // Map pool for the tournament
        mapPool: [
            'Ascent',
            'Bind',
            'Haven',
            'Split',
            'Icebox',
            'Breeze',
            'Fracture'
        ]
    }
];

// Current active tournament
const currentTournament = tournaments[0];

/**
 * UTILITY FUNCTIONS FOR TOURNAMENTS
 */

// Get current tournament
function getCurrentTournament() {
    return currentTournament;
}

// Get tournament by ID
function getTournamentById(tournamentId) {
    return tournaments.find(tournament => tournament.id === tournamentId);
}

// Get tournament status
function getTournamentStatus() {
    return currentTournament.status;
}

// Get next match in the tournament
function getNextMatch() {
    const tournament = getCurrentTournament();

    // Check semifinals first
    for (let match of tournament.bracket.semifinals) {
        if (match.status === 'upcoming') {
            return match;
        }
    }

    // Check final
    if (tournament.bracket.final.status === 'upcoming') {
        return tournament.bracket.final;
    }

    return null;
}

// Get completed matches
function getCompletedMatches() {
    const tournament = getCurrentTournament();
    const completed = [];

    // Add completed quarterfinals
    tournament.bracket.quarterfinals.forEach(match => {
        if (match.status === 'completed') {
            completed.push(match);
        }
    });

    // Add completed semifinals
    tournament.bracket.semifinals.forEach(match => {
        if (match.status === 'completed') {
            completed.push(match);
        }
    });

    // Add completed final
    if (tournament.bracket.final.status === 'completed') {
        completed.push(tournament.bracket.final);
    }

    return completed;
}

// Get upcoming matches
function getUpcomingMatches() {
    const tournament = getCurrentTournament();
    const upcoming = [];

    // Add upcoming semifinals
    tournament.bracket.semifinals.forEach(match => {
        if (match.status === 'upcoming') {
            upcoming.push(match);
        }
    });

    // Add upcoming final
    if (tournament.bracket.final.status === 'upcoming') {
        upcoming.push(tournament.bracket.final);
    }

    return upcoming;
}

// Update match result
function updateMatchResult(matchId, winnerId) {
    const tournament = getCurrentTournament();

    // Find and update the match
    const allMatches = [
        ...tournament.bracket.quarterfinals,
        ...tournament.bracket.semifinals,
        tournament.bracket.final
    ];

    const match = allMatches.find(m => m.id === matchId);
    if (match) {
        match.winner = winnerId;
        match.status = 'completed';

        // Update team records
        updateTeamRecord(winnerId, true);
        const loserId = match.team1 === winnerId ? match.team2 : match.team1;
        updateTeamRecord(loserId, false);
        eliminateTeam(loserId);
    }
}

// Get tournament progress percentage
function getTournamentProgress() {
    const tournament = getCurrentTournament();
    const totalMatches = tournament.bracket.quarterfinals.length +
                        tournament.bracket.semifinals.length + 1; // +1 for final

    const completedMatches = getCompletedMatches().length;

    return Math.round((completedMatches / totalMatches) * 100);
}

/**
 * UNIFIED MATCH FUNCTIONS
 * These functions work with the new unified tournament structure
 */

// Get all matches from current tournament (flattened from bracket structure)
function getAllMatches() {
    const tournament = getCurrentTournament();
    if (!tournament) return [];

    const allMatches = [
        ...tournament.bracket.quarterfinals,
        ...tournament.bracket.semifinals,
        tournament.bracket.final
    ];

    return allMatches;
}

// Get match by ID (works with bracket match IDs)
function getMatchById(matchId) {
    const allMatches = getAllMatches();
    return allMatches.find(match => match.id === matchId);
}

// Get matches by status
function getMatchesByStatus(status) {
    const allMatches = getAllMatches();
    return allMatches.filter(match => match.status === status);
}

// Get matches by round
function getMatchesByRound(round) {
    const allMatches = getAllMatches();
    return allMatches.filter(match => match.matchDetails.round === round);
}

// Get upcoming matches (next 3)
function getUpcomingMatchesLimit(limit = 3) {
    const allMatches = getAllMatches();
    return allMatches
        .filter(match => match.status === 'upcoming')
        .sort((a, b) => new Date(a.matchDetails.scheduledTime) - new Date(b.matchDetails.scheduledTime))
        .slice(0, limit);
}

// Get recent completed matches (last 3)
function getRecentMatches(limit = 3) {
    const allMatches = getAllMatches();
    return allMatches
        .filter(match => match.status === 'completed')
        .sort((a, b) => new Date(b.matchDetails.endTime) - new Date(a.matchDetails.endTime))
        .slice(0, limit);
}

// Get live matches
function getLiveMatches() {
    const allMatches = getAllMatches();
    return allMatches.filter(match => match.status === 'live');
}

// Format match time for display
function formatMatchTime(timestamp) {
    const date = new Date(timestamp);
    const options = {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
    };
    return date.toLocaleDateString('en-US', options);
}

// Get match duration in minutes
function getMatchDuration(match) {
    if (!match.matchDetails.actualStartTime || !match.matchDetails.endTime) return null;

    const start = new Date(match.matchDetails.actualStartTime);
    const end = new Date(match.matchDetails.endTime);
    return Math.round((end - start) / (1000 * 60)); // Convert to minutes
}

// Get match score string (e.g., "2-1")
function getMatchScoreString(match) {
    if (match.status === 'upcoming') return 'vs';
    return `${match.matchDetails.score.team1}-${match.matchDetails.score.team2}`;
}

// Update match result (enhanced version)
function updateMatchResultDetailed(matchId, winnerId, score, maps, endTime = null) {
    const match = getMatchById(matchId);
    if (!match) return false;

    match.status = 'completed';
    match.matchDetails.score = score;
    match.matchDetails.maps = maps || [];
    match.matchDetails.endTime = endTime || new Date().toISOString();

    // For best-of-1 matches, the map winner is the match winner
    if (match.matchDetails.bestOf === 1 && maps && maps.length === 1) {
        match.winner = maps[0].winner;
    } else {
        match.winner = winnerId;
    }

    return true;
}

// Start a match (set actual start time)
function startMatch(matchId) {
    const match = getMatchById(matchId);
    if (!match) return false;

    match.status = 'live';
    match.matchDetails.actualStartTime = new Date().toISOString();

    return true;
}

/**
 * TIME UTILITY FUNCTIONS FOR INDIAN TIME (IST)
 */

// Convert simple time input to full IST datetime string
function getISTDateTime(date, time) {
    // Ensure date is in YYYY-MM-DD format
    const dateStr = date.split('T')[0] || date;
    
    // Handle time input
    let hours, minutes;
    if (typeof time === 'number') {
        // If time is just a number like 15, treat it as hours
        hours = time;
        minutes = 0;
    } else if (typeof time === 'string') {
        // If time is a string like "15:30", split into hours and minutes
        const [h, m] = time.split(':');
        hours = parseInt(h);
        minutes = parseInt(m) || 0;
    }

    // Format with IST timezone
    return `${dateStr}T${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00+05:30`;
}

// Format match time for display in IST
function formatISTTime(timestamp) {
    const date = new Date(timestamp);
    const options = {
        timeZone: 'Asia/Kolkata',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    };
    return date.toLocaleString('en-IN', options);
}

// Get match duration in minutes
function getMatchDuration(match) {
    if (!match.matchDetails.actualStartTime || !match.matchDetails.endTime) return null;
    const start = new Date(match.matchDetails.actualStartTime);
    const end = new Date(match.matchDetails.endTime);
    return Math.round((end - start) / (1000 * 60));
}

/**
 * EASY TOURNAMENT CREATION EXAMPLE
 *
 * To add a new tournament, copy this template and modify:
 *
 * 1. Copy the "Neon Mania" tournament structure above
 * 2. Change the basic info (id, name, dates, etc.)
 * 3. Update team assignments in quarterfinals
 * 4. Adjust match times as needed
 * 5. Add to tournaments array and update currentTournament!
 *
 * Everything is in one place - no need to edit multiple files!
 */

// Export for use in other files (if using modules)
// export { tournaments, currentTournament, getCurrentTournament, getTournamentById, getNextMatch, getCompletedMatches, getUpcomingMatches, updateMatchResult, getTournamentProgress, getAllMatches, getMatchById, getMatchesByStatus, getMatchesByRound, getUpcomingMatchesLimit, getRecentMatches, getLiveMatches, formatMatchTime, getMatchDuration, getMatchScoreString, updateMatchResultDetailed, startMatch };

// Export functions for use in other files
window.TournamentTime = {
    formatISTTime,
    getMatchDuration,
    getISTDateTime
};
